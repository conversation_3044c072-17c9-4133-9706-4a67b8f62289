<?php

namespace Deployer;

/**
 * Hosts
 */
host('production')
    ->setHostname('************')
    ->setPort('2232')
    ->setConfigFile('~/.ssh/config')
//    ->setIdentityFile('~/.ssh/id_rsa')
    ->setRemoteUser('copex')
    ->set('docker',true)
    ->setDeployPath('{{deployment_root_path}}/production')
    ->setForwardAgent(true)
    ->setLabels([
        'env'  => ENV_PRODUCTION,
    ])
//    ->set('rsync_src', __DIR__)
//    ->set('rsync_dest','{{release_path}}')
    ->set('branch', 'master');
